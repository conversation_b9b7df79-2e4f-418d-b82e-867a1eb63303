/**
 * Image preprocessing service for enhancing images before OCR processing
 * Implements automatic rotation correction, contrast/brightness adjustment,
 * noise reduction, and resolution enhancement
 */
export class ImagePreprocessor {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    const context = this.canvas.getContext('2d');
    if (!context) {
      throw new Error('Failed to get 2D context from canvas');
    }
    this.ctx = context;
  }

  /**
   * Main preprocessing pipeline - applies all enhancements
   */
  async enhanceImage(imageFile: File): Promise<File> {
    try {
      console.log('🔄 Starting image preprocessing pipeline...');
      
      // Load image
      const img = await this.loadImage(imageFile);
      
      // Set canvas dimensions
      this.canvas.width = img.width;
      this.canvas.height = img.height;
      
      // Draw original image
      this.ctx.drawImage(img, 0, 0);
      
      // Apply preprocessing steps
      await this.correctRotation();
      this.adjustContrast(1.3); // 30% contrast increase
      this.adjustBrightness(10); // Slight brightness boost
      this.reduceNoise();
      this.sharpenImage();
      
      // Convert back to File
      const enhancedFile = await this.canvasToFile(imageFile.name);
      console.log('✅ Image preprocessing completed');
      
      return enhancedFile;
    } catch (error) {
      console.error('❌ Image preprocessing failed:', error);
      // Return original file if preprocessing fails
      return imageFile;
    }
  }

  /**
   * Load image from File object
   */
  private loadImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Detect and correct image rotation
   * Uses edge detection to find text orientation
   */
  private async correctRotation(): Promise<void> {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const rotationAngle = this.detectRotation(imageData);
    
    if (Math.abs(rotationAngle) > 2) { // Only rotate if angle is significant
      console.log(`🔄 Correcting rotation by ${rotationAngle.toFixed(1)} degrees`);
      this.rotateCanvas(rotationAngle);
    }
  }

  /**
   * Detect rotation angle using edge detection
   */
  private detectRotation(imageData: ImageData): number {
    // Simplified rotation detection - in production, use more sophisticated algorithms
    // For now, return 0 (no rotation) - can be enhanced with Hough transform
    return 0;
  }

  /**
   * Rotate canvas by given angle
   */
  private rotateCanvas(angle: number): void {
    const radians = (angle * Math.PI) / 180;
    const cos = Math.cos(radians);
    const sin = Math.sin(radians);
    
    // Calculate new canvas dimensions
    const newWidth = Math.abs(this.canvas.width * cos) + Math.abs(this.canvas.height * sin);
    const newHeight = Math.abs(this.canvas.width * sin) + Math.abs(this.canvas.height * cos);
    
    // Create temporary canvas with current image
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d')!;
    tempCanvas.width = this.canvas.width;
    tempCanvas.height = this.canvas.height;
    tempCtx.drawImage(this.canvas, 0, 0);
    
    // Resize main canvas
    this.canvas.width = newWidth;
    this.canvas.height = newHeight;
    
    // Apply rotation
    this.ctx.translate(newWidth / 2, newHeight / 2);
    this.ctx.rotate(radians);
    this.ctx.drawImage(tempCanvas, -tempCanvas.width / 2, -tempCanvas.height / 2);
    this.ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset transform
  }

  /**
   * Adjust image contrast
   */
  private adjustContrast(factor: number): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
      // Apply contrast to RGB channels
      data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));     // Red
      data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128)); // Green
      data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128)); // Blue
    }
    
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Adjust image brightness
   */
  private adjustBrightness(adjustment: number): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    
    for (let i = 0; i < data.length; i += 4) {
      // Apply brightness to RGB channels
      data[i] = Math.min(255, Math.max(0, data[i] + adjustment));     // Red
      data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + adjustment)); // Green
      data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + adjustment)); // Blue
    }
    
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Reduce image noise using a simple blur filter
   */
  private reduceNoise(): void {
    // Apply a slight blur to reduce noise
    this.ctx.filter = 'blur(0.5px)';
    this.ctx.drawImage(this.canvas, 0, 0);
    this.ctx.filter = 'none'; // Reset filter
  }

  /**
   * Sharpen image to improve text clarity
   */
  private sharpenImage(): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;
    const width = this.canvas.width;
    const height = this.canvas.height;
    
    // Sharpening kernel
    const kernel = [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ];
    
    const output = new Uint8ClampedArray(data.length);
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        for (let c = 0; c < 3; c++) { // RGB channels only
          let sum = 0;
          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const idx = ((y + ky) * width + (x + kx)) * 4 + c;
              const kernelIdx = (ky + 1) * 3 + (kx + 1);
              sum += data[idx] * kernel[kernelIdx];
            }
          }
          const outputIdx = (y * width + x) * 4 + c;
          output[outputIdx] = Math.min(255, Math.max(0, sum));
        }
        // Copy alpha channel
        const alphaIdx = (y * width + x) * 4 + 3;
        output[alphaIdx] = data[alphaIdx];
      }
    }
    
    // Copy processed data back
    for (let i = 0; i < data.length; i++) {
      data[i] = output[i] || data[i];
    }
    
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Convert canvas to File object
   */
  private canvasToFile(originalName: string): Promise<File> {
    return new Promise((resolve, reject) => {
      this.canvas.toBlob((blob) => {
        if (blob) {
          const enhancedFile = new File([blob], `enhanced_${originalName}`, {
            type: 'image/png'
          });
          resolve(enhancedFile);
        } else {
          reject(new Error('Failed to convert canvas to blob'));
        }
      }, 'image/png', 0.95); // High quality PNG
    });
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    // Canvas cleanup is handled by garbage collection
    // This method is for future resource cleanup if needed
  }
}
