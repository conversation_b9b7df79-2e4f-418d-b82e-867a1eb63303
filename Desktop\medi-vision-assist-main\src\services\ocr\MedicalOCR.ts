import { createWorker } from 'tesseract.js';
import { OCRResult, OCRConfig, MedicineInfo, MedicineIdentificationResult } from '@/types/ocr';
import { ImagePreprocessor } from './ImagePreprocessor';

/**
 * Medical OCR class for extracting text from medicine images using Tesseract.js
 */
export class MedicalOCR {
  /**
   * Create a language-specific OCR configuration
   */
  static createLanguageConfig(language: 'eng' | 'deu' | 'ara' | 'multi'): OCRConfig {
    const baseConfig: OCRConfig = {
      minConfidence: 50,
      debug: false,
      tesseractOptions: {
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz********** .-+%®™&/()[]',
        tessedit_pageseg_mode: '8',
        tessedit_ocr_engine_mode: '1',
        preserve_interword_spaces: '1',
        user_defined_dpi: '300'
      }
    };

    switch (language) {
      case 'deu':
        return {
          ...baseConfig,
          language: 'deu',
          languages: ['deu'],
          tesseractOptions: {
            ...baseConfig.tesseractOptions,
            tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzäöüßÄÖÜ********** .-+%®™&/()[]'
          }
        };

      case 'ara':
        return {
          ...baseConfig,
          language: 'ara',
          languages: ['ara'],
          tesseractOptions: {
            ...baseConfig.tesseractOptions,
            tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\u0600-\u06FF********** .-+%®™&/()[]'
          }
        };

      case 'multi':
        return {
          ...baseConfig,
          languages: ['eng', 'deu', 'ara'],
          autoDetectLanguage: true,
          tesseractOptions: {
            ...baseConfig.tesseractOptions,
            tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzäöüßÄÖÜ\u0600-\u06FF********** .-+%®™&/()[]'
          }
        };

      default: // 'eng'
        return {
          ...baseConfig,
          language: 'eng',
          languages: ['eng']
        };
    }
  }
  private worker: any | null = null;
  private isInitialized = false;
  private config: OCRConfig;
  private preprocessor: ImagePreprocessor;

  constructor(config: OCRConfig = {}) {
    this.config = {
      language: 'eng',
      languages: ['eng'], // Default to English only
      autoDetectLanguage: false,
      minConfidence: 50, // Lowered for better medicine detection
      debug: false,
      tesseractOptions: {
        // Enhanced character whitelist for medicine labels
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz********** .-+%®™&/()[]',

        // Optimized page segmentation for medicine labels
        tessedit_pageseg_mode: '8', // PSM.SINGLE_WORD - better for medicine names

        // Additional OCR optimizations for medicine text
        tessedit_ocr_engine_mode: '1', // LSTM OCR Engine only
        preserve_interword_spaces: '1', // Preserve spaces between words
        user_defined_dpi: '300', // High DPI for better text recognition

        // Text detection improvements
        textord_min_linesize: '2.5', // Minimum line size
        textord_tabfind_show_vlines: '0', // Don't show vertical lines
        textord_tablefind_good_width: '3', // Good table width

        // Character recognition improvements
        classify_enable_learning: '0', // Disable adaptive learning for consistency
        classify_enable_adaptive_matcher: '0', // Disable adaptive matching

        // Language model improvements
        load_system_dawg: '0', // Don't load system dictionary
        load_freq_dawg: '0', // Don't load frequency dictionary
        load_unambig_dawg: '0', // Don't load unambiguous dictionary
        load_punc_dawg: '0', // Don't load punctuation dictionary
        load_number_dawg: '0', // Don't load number dictionary
        load_bigram_dawg: '0', // Don't load bigram dictionary

        // Edge detection and line finding
        edges_max_children_per_outline: '40',
        edges_children_count_limit: '45',
        edges_min_nonhole: '12',

        // Improve text line detection
        textord_heavy_nr: '1',
        textord_show_initial_words: '0',
        textord_show_new_words: '0'
      },
      ...config,
    };
    this.preprocessor = new ImagePreprocessor();
  }

  /**
   * Initialize the Tesseract worker
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Initializing Tesseract OCR worker...');

      // Create worker with multi-language support
      const languages = this.config.languages && this.config.languages.length > 0
        ? this.config.languages.join('+')
        : (this.config.language || 'eng');

      console.log(`🌐 Loading languages: ${languages}`);
      this.worker = await createWorker(languages);

      console.log('🔄 Worker created and initialized successfully');

      // Set custom parameters if provided
      if (this.config.tesseractOptions) {
        console.log('🔄 Setting custom parameters...');
        await this.worker.setParameters(this.config.tesseractOptions);
      }

      this.isInitialized = true;
      console.log('✅ Tesseract OCR worker ready for use');
    } catch (error) {
      console.error('❌ Failed to initialize Tesseract worker:', error);
      this.worker = null;
      this.isInitialized = false;
      throw new Error(`OCR initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract text from an image file
   */
  async extractText(imageFile: File): Promise<OCRResult> {
    if (!this.isInitialized || !this.worker) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      console.log(`🔄 Starting OCR processing for: ${imageFile.name}`);

      // Step 1: Preprocess image for better OCR accuracy
      const enhancedImage = await this.preprocessor.enhanceImage(imageFile);
      console.log('✅ Image preprocessing completed');

      // Step 2: Detect language if auto-detection is enabled
      let detectedLanguage = this.config.language || 'eng';
      if (this.config.autoDetectLanguage) {
        detectedLanguage = await this.detectLanguage(enhancedImage);
        console.log(`🌐 Detected language: ${detectedLanguage}`);
      }

      // Step 3: Optimize Tesseract configuration for this image
      await this.optimizeConfigurationForImage(enhancedImage);

      // Step 4: Perform OCR on enhanced image
      const { data } = await this.worker!.recognize(enhancedImage);
      const processingTime = Date.now() - startTime;

      const result: OCRResult = {
        text: data.text.trim(),
        confidence: data.confidence,
        processingTime,
        words: data.words || [],
        success: true,
      };

      console.log(`✅ OCR completed in ${processingTime}ms with ${data.confidence.toFixed(1)}% confidence`);
      
      if (this.config.debug) {
        console.log('📝 Extracted text:', result.text);
        console.log('📊 Word details:', result.words);
      }

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown OCR error';
      
      console.error('❌ OCR processing failed:', errorMessage);

      return {
        text: '',
        confidence: 0,
        processingTime,
        words: [],
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Extract medicine-specific information from OCR text
   */
  extractMedicineInfo(ocrText: string): MedicineInfo {
    const cleanText = this.cleanText(ocrText);
    const potentialNames = this.extractPotentialMedicineNames(cleanText);
    const dosageInfo = this.extractDosageInfo(cleanText);
    const medicineType = this.identifyMedicineType(cleanText);

    return {
      potentialNames,
      dosageInfo,
      medicineType,
      confidence: this.calculateMedicineConfidence(potentialNames, dosageInfo),
      rawText: ocrText,
      cleanedText: cleanText,
    };
  }

  /**
   * Complete medicine identification process
   */
  async identifyMedicine(imageFile: File): Promise<MedicineIdentificationResult> {
    try {
      console.log('🚀 Starting complete medicine identification process...');
      
      // Step 1: Extract text using OCR
      const ocrResult = await this.extractText(imageFile);
      
      if (!ocrResult.success) {
        return {
          ocrResult,
          medicineInfo: this.getEmptyMedicineInfo(),
          success: false,
        };
      }

      // Step 2: Extract medicine-specific information
      const medicineInfo = this.extractMedicineInfo(ocrResult.text);
      
      // Step 3: Identify the most likely medicine
      const identifiedMedicine = this.selectBestMedicine(medicineInfo.potentialNames);

      const result: MedicineIdentificationResult = {
        ocrResult,
        medicineInfo,
        identifiedMedicine,
        success: ocrResult.success && medicineInfo.confidence >= (this.config.minConfidence || 60),
      };

      console.log(`✅ Medicine identification completed. Success: ${result.success}`);
      if (result.identifiedMedicine) {
        console.log(`🎯 Identified medicine: ${result.identifiedMedicine}`);
      }

      return result;
    } catch (error) {
      console.error('❌ Medicine identification failed:', error);
      
      return {
        ocrResult: {
          text: '',
          confidence: 0,
          processingTime: 0,
          words: [],
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        medicineInfo: this.getEmptyMedicineInfo(),
        success: false,
      };
    }
  }

  /**
   * Clean and normalize text for better processing
   */
  private cleanText(text: string): string {
    return text
      .replace(/[^\w\s\d.-]/g, ' ') // Remove special characters except dots and hyphens
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .toLowerCase();
  }

  /**
   * Extract potential medicine names from cleaned text
   */
  private extractPotentialMedicineNames(cleanText: string): string[] {
    const words = cleanText.split(' ');
    const potentialNames: string[] = [];

    // Look for medicine-like patterns
    for (let i = 0; i < words.length; i++) {
      const word = words[i];

      // Skip very short words
      if (word.length < 3) continue;

      // Look for medicine name patterns
      if (this.isMedicineNamePattern(word)) {
        potentialNames.push(word);

        // Enhanced compound name detection
        this.extractCompoundNames(words, i, potentialNames);
      }
    }

    // Fallback: Look for medicine-like sequences in the original text
    const fallbackNames = this.extractFallbackMedicineNames(cleanText);
    potentialNames.push(...fallbackNames);

    // Filter and rank potential names
    const filteredNames = this.filterAndRankMedicineNames(potentialNames);

    return [...new Set(filteredNames)]; // Remove duplicates
  }

  /**
   * Extract compound medicine names (e.g., "Tylenol Extra Strength", "Advil Cold & Sinus")
   */
  private extractCompoundNames(words: string[], currentIndex: number, potentialNames: string[]): void {
    const word = words[currentIndex];

    // Check for 2-word compounds
    if (currentIndex + 1 < words.length) {
      const nextWord = words[currentIndex + 1];
      if (this.isMedicineModifier(nextWord) || this.isMedicineComponent(nextWord)) {
        potentialNames.push(`${word} ${nextWord}`);

        // Check for 3-word compounds
        if (currentIndex + 2 < words.length) {
          const thirdWord = words[currentIndex + 2];
          if (this.isMedicineModifier(thirdWord) || this.isMedicineComponent(thirdWord)) {
            potentialNames.push(`${word} ${nextWord} ${thirdWord}`);
          }
        }
      }
    }
  }

  /**
   * Check if a word is a medicine component (not just modifier)
   */
  private isMedicineComponent(word: string): boolean {
    const components = [
      'cold', 'flu', 'sinus', 'allergy', 'cough', 'fever', 'pain', 'relief',
      'day', 'night', 'pm', 'am', 'multi', 'symptom', 'complete', 'advanced',
      'liquid', 'tablet', 'capsule', 'gel', 'cream', 'ointment', 'spray'
    ];
    return components.includes(word);
  }

  /**
   * Fallback medicine name extraction using different strategies
   */
  private extractFallbackMedicineNames(text: string): string[] {
    const fallbackNames: string[] = [];

    // Strategy 1: Look for capitalized sequences
    const capitalizedPattern = /\b[A-Z][a-z]{3,}(?:\s+[A-Z][a-z]+)*\b/g;
    const capitalizedMatches = text.match(capitalizedPattern) || [];

    for (const match of capitalizedMatches) {
      if (match.length >= 4 && match.length <= 30) {
        fallbackNames.push(match.toLowerCase());
      }
    }

    // Strategy 2: Look for medicine-like patterns with numbers (dosage indicators)
    const dosagePattern = /\b([a-z]{4,})\s+\d+\s*(mg|g|ml|mcg|iu)\b/gi;
    const dosageMatches = text.match(dosagePattern) || [];

    for (const match of dosageMatches) {
      const medicineName = match.split(/\s+\d+/)[0];
      if (medicineName && medicineName.length >= 4) {
        fallbackNames.push(medicineName.toLowerCase());
      }
    }

    return fallbackNames;
  }

  /**
   * Filter and rank medicine names by confidence
   */
  private filterAndRankMedicineNames(names: string[]): string[] {
    return names
      .filter(name => name.length >= 3 && name.length <= 50)
      .sort((a, b) => {
        // Prioritize longer, more specific names
        const aScore = this.calculateNameConfidence(a);
        const bScore = this.calculateNameConfidence(b);
        return bScore - aScore;
      })
      .slice(0, 10); // Limit to top 10 candidates
  }

  /**
   * Calculate confidence score for a medicine name
   */
  private calculateNameConfidence(name: string): number {
    let score = 0;

    // Length bonus (optimal length is 6-15 characters)
    if (name.length >= 6 && name.length <= 15) {
      score += 20;
    } else if (name.length >= 4) {
      score += 10;
    }

    // Compound name bonus
    if (name.includes(' ')) {
      score += 15;
    }

    // Known medicine bonus
    const knownMedicines = ['aspirin', 'tylenol', 'advil', 'ibuprofen', 'acetaminophen'];
    if (knownMedicines.some(med => name.includes(med))) {
      score += 30;
    }

    // Pharmaceutical pattern bonus
    if (/^[a-z]+[a-z]{2,}(in|ol|an|ex|ine|ate|ide)$/.test(name)) {
      score += 25;
    }

    return score;
  }

  /**
   * Check if a word matches medicine name patterns
   */
  private isMedicineNamePattern(word: string): boolean {
    // Get language-specific patterns
    const currentLanguage = this.config.language || 'eng';
    const patterns = this.getLanguageSpecificPatterns(currentLanguage);

    // Enhanced medicine prefixes (language-agnostic)
    const medicinePrefixes = [
      'anti', 'pro', 'pre', 'meta', 'para', 'hydro', 'chlor', 'sulfa', 'ceph'
    ];

    // Check minimum length
    if (word.length < 3) return false;

    // Check for exact matches with known medicines
    if (patterns.knownMedicines.some(med => word.includes(med))) {
      return true;
    }

    // Check for medicine endings
    if (word.length >= 4 && patterns.medicineEndings.some(ending => word.endsWith(ending))) {
      return true;
    }

    // Check for medicine prefixes
    if (word.length >= 6 && medicinePrefixes.some(prefix => word.startsWith(prefix))) {
      return true;
    }

    // Language-specific pattern matching
    if (currentLanguage === 'ara') {
      // Arabic-specific patterns
      if (/[\u0600-\u06FF]/.test(word) && word.length >= 3) {
        return true;
      }
    } else if (currentLanguage === 'deu') {
      // German-specific patterns
      const germanMedicinePattern = /^[A-ZÄÖÜ][a-zäöüß]{2,}[a-zäöüß]*$/;
      if (germanMedicinePattern.test(word) && word.length >= 5) {
        return true;
      }
    } else {
      // English-specific patterns (capital letters followed by lowercase)
      const pharmaPattern = /^[A-Z][a-z]{2,}[A-Z]?[a-z]*$/;
      if (pharmaPattern.test(word) && word.length >= 5) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a word is a medicine modifier (strength, type, etc.)
   */
  private isMedicineModifier(word: string): boolean {
    // Get language-specific modifiers
    const currentLanguage = this.config.language || 'eng';
    const patterns = this.getLanguageSpecificPatterns(currentLanguage);

    return patterns.modifiers.includes(word);
  }

  /**
   * Extract dosage information from text
   */
  private extractDosageInfo(cleanText: string): string | undefined {
    const dosagePatterns = [
      /(\d+)\s*(mg|g|ml|mcg|iu)/gi,
      /(\d+)\s*(milligram|gram|milliliter|microgram)/gi,
    ];

    for (const pattern of dosagePatterns) {
      const match = cleanText.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return undefined;
  }

  /**
   * Identify medicine type/category
   */
  private identifyMedicineType(cleanText: string): string | undefined {
    const typeKeywords = {
      'pain relief': ['pain', 'relief', 'analgesic', 'aspirin', 'ibuprofen', 'tylenol'],
      'antibiotic': ['antibiotic', 'amoxicillin', 'penicillin', 'azithromycin'],
      'vitamin': ['vitamin', 'supplement', 'multivitamin'],
      'allergy': ['allergy', 'antihistamine', 'benadryl', 'claritin'],
    };

    for (const [type, keywords] of Object.entries(typeKeywords)) {
      if (keywords.some(keyword => cleanText.includes(keyword))) {
        return type;
      }
    }

    return undefined;
  }

  /**
   * Calculate confidence score for medicine identification
   */
  private calculateMedicineConfidence(potentialNames: string[], dosageInfo?: string): number {
    let confidence = 0;

    // Base confidence from having potential names
    if (potentialNames.length > 0) {
      confidence += 40;
    }

    // Bonus for multiple potential names
    confidence += Math.min(potentialNames.length * 10, 30);

    // Bonus for having dosage information
    if (dosageInfo) {
      confidence += 20;
    }

    // Bonus for recognizable medicine names
    const recognizableNames = potentialNames.filter(name => 
      ['aspirin', 'tylenol', 'advil', 'ibuprofen', 'amoxicillin', 'panadol'].some(known => 
        name.includes(known)
      )
    );
    confidence += recognizableNames.length * 15;

    return Math.min(confidence, 100);
  }

  /**
   * Select the best medicine from potential names
   */
  private selectBestMedicine(potentialNames: string[]): string | undefined {
    if (potentialNames.length === 0) return undefined;

    // Prioritize longer, more specific names
    return potentialNames.sort((a, b) => b.length - a.length)[0];
  }

  /**
   * Get empty medicine info structure
   */
  private getEmptyMedicineInfo(): MedicineInfo {
    return {
      potentialNames: [],
      confidence: 0,
      rawText: '',
      cleanedText: '',
    };
  }

  /**
   * Optimize Tesseract configuration based on image characteristics
   */
  private async optimizeConfigurationForImage(imageFile: File): Promise<void> {
    try {
      // Analyze image characteristics
      const imageAnalysis = await this.analyzeImageCharacteristics(imageFile);

      // Adjust page segmentation mode based on image content
      let psmMode = '8'; // Default: SINGLE_WORD

      if (imageAnalysis.hasMultipleLines) {
        psmMode = '6'; // SINGLE_BLOCK
      } else if (imageAnalysis.hasLargeText) {
        psmMode = '7'; // SINGLE_TEXT_LINE
      } else if (imageAnalysis.hasSmallText) {
        psmMode = '10'; // SINGLE_CHAR - for very small text
      }

      // Apply optimized configuration
      await this.worker!.setParameters({
        tessedit_pageseg_mode: psmMode,
        // Adjust confidence thresholds based on image quality
        tessedit_char_unblacklist: imageAnalysis.isLowQuality ? '|' : '',
        // Enable/disable certain features based on image characteristics
        textord_really_old_xheight: imageAnalysis.hasVariableTextSize ? '1' : '0',
        textord_min_xheight: imageAnalysis.hasSmallText ? '10' : '20'
      });

      console.log(`🔧 Optimized OCR config: PSM=${psmMode}, Quality=${imageAnalysis.isLowQuality ? 'Low' : 'Good'}`);
    } catch (error) {
      console.warn('⚠️ Failed to optimize OCR configuration:', error);
      // Continue with default configuration
    }
  }

  /**
   * Analyze image characteristics to optimize OCR settings
   */
  private async analyzeImageCharacteristics(imageFile: File): Promise<{
    hasMultipleLines: boolean;
    hasLargeText: boolean;
    hasSmallText: boolean;
    hasVariableTextSize: boolean;
    isLowQuality: boolean;
  }> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        // Scale down for analysis
        const scale = Math.min(200 / img.width, 200 / img.height);
        canvas.width = img.width * scale;
        canvas.height = img.height * scale;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

        // Simple heuristic analysis
        const analysis = {
          hasMultipleLines: canvas.height > 50, // Likely multiple lines if tall
          hasLargeText: canvas.width > 150, // Likely large text if wide
          hasSmallText: canvas.width < 100 && canvas.height < 30, // Small text
          hasVariableTextSize: canvas.width > 100 && canvas.height > 40, // Variable sizes
          isLowQuality: this.detectLowQuality(imageData) // Quality assessment
        };

        resolve(analysis);
      };

      img.onerror = () => {
        // Default analysis if image loading fails
        resolve({
          hasMultipleLines: false,
          hasLargeText: false,
          hasSmallText: false,
          hasVariableTextSize: false,
          isLowQuality: true
        });
      };

      img.src = URL.createObjectURL(imageFile);
    });
  }

  /**
   * Detect if image is low quality using simple metrics
   */
  private detectLowQuality(imageData: ImageData): boolean {
    const data = imageData.data;
    let blurScore = 0;
    let contrastScore = 0;

    // Simple blur detection (check for sharp edges)
    for (let i = 0; i < data.length - 8; i += 4) {
      const current = data[i] + data[i + 1] + data[i + 2];
      const next = data[i + 4] + data[i + 5] + data[i + 6];
      const diff = Math.abs(current - next);

      if (diff > 100) blurScore++;
    }

    // Simple contrast detection
    let min = 255 * 3;
    let max = 0;

    for (let i = 0; i < data.length; i += 4) {
      const brightness = data[i] + data[i + 1] + data[i + 2];
      min = Math.min(min, brightness);
      max = Math.max(max, brightness);
    }

    contrastScore = max - min;

    // Low quality if low blur score or low contrast
    return blurScore < (data.length / 4) * 0.1 || contrastScore < 200;
  }

  /**
   * Detect the primary language in the image
   */
  private async detectLanguage(imageFile: File): Promise<string> {
    try {
      // Create a temporary worker for language detection
      const detectionWorker = await createWorker('eng+ara+deu');

      // Perform quick OCR with multiple languages
      const { data } = await detectionWorker.recognize(imageFile);

      // Analyze the text to determine the most likely language
      const detectedLang = this.analyzeTextLanguage(data.text);

      await detectionWorker.terminate();
      return detectedLang;
    } catch (error) {
      console.warn('⚠️ Language detection failed, defaulting to English:', error);
      return 'eng';
    }
  }

  /**
   * Analyze text to determine language
   */
  private analyzeTextLanguage(text: string): string {
    const cleanText = text.toLowerCase().trim();

    // Arabic detection - look for Arabic characters
    if (/[\u0600-\u06FF]/.test(text)) {
      return 'ara';
    }

    // German detection - look for German-specific patterns
    const germanPatterns = [
      /\b(der|die|das|und|oder|mit|für|von|zu|bei|nach|über|unter|gegen|ohne|durch)\b/g,
      /[äöüß]/g,
      /\b\w*ung\b/g, // German -ung endings
      /\b\w*keit\b/g, // German -keit endings
      /\b\w*heit\b/g // German -heit endings
    ];

    let germanScore = 0;
    for (const pattern of germanPatterns) {
      const matches = cleanText.match(pattern);
      if (matches) {
        germanScore += matches.length;
      }
    }

    // English detection - look for English-specific patterns
    const englishPatterns = [
      /\b(the|and|or|with|for|from|to|at|by|of|in|on|is|are|was|were|have|has|had)\b/g,
      /\b\w*ing\b/g, // English -ing endings
      /\b\w*tion\b/g, // English -tion endings
      /\b\w*ly\b/g // English -ly endings
    ];

    let englishScore = 0;
    for (const pattern of englishPatterns) {
      const matches = cleanText.match(pattern);
      if (matches) {
        englishScore += matches.length;
      }
    }

    // Determine language based on scores
    if (germanScore > englishScore && germanScore > 2) {
      return 'deu';
    } else if (englishScore > 0) {
      return 'eng';
    }

    // Default to English if no clear pattern
    return 'eng';
  }

  /**
   * Get language-specific medicine patterns
   */
  private getLanguageSpecificPatterns(language: string): {
    medicineEndings: string[];
    knownMedicines: string[];
    modifiers: string[];
  } {
    switch (language) {
      case 'deu': // German
        return {
          medicineEndings: ['in', 'ol', 'an', 'ex', 'ine', 'ate', 'ide', 'ium', 'one'],
          knownMedicines: [
            'aspirin', 'ibuprofen', 'paracetamol', 'diclofenac', 'naproxen',
            'omeprazol', 'pantoprazol', 'simvastatin', 'atorvastatin',
            'metformin', 'insulin', 'lisinopril', 'metoprolol'
          ],
          modifiers: [
            'stark', 'extra', 'forte', 'plus', 'max', 'schnell', 'retard',
            'tabletten', 'kapseln', 'tropfen', 'salbe', 'creme', 'spray',
            'schmerz', 'fieber', 'erkältung', 'allergie', 'husten'
          ]
        };

      case 'ara': // Arabic
        return {
          medicineEndings: ['ين', 'ول', 'ان', 'اكس', 'ين', 'ات', 'يد'],
          knownMedicines: [
            'أسبرين', 'ايبوبروفين', 'باراسيتامول', 'ديكلوفيناك',
            'أوميبرازول', 'ميتفورمين', 'انسولين'
          ],
          modifiers: [
            'قوي', 'اضافي', 'فورت', 'بلس', 'ماكس', 'سريع',
            'أقراص', 'كبسولات', 'قطرات', 'مرهم', 'كريم', 'بخاخ',
            'ألم', 'حمى', 'برد', 'حساسية', 'سعال'
          ]
        };

      default: // English
        return {
          medicineEndings: [
            'in', 'ol', 'an', 'ex', 'ine', 'ate', 'ide', 'ium', 'one', 'ase',
            'cin', 'lin', 'pam', 'zole', 'pine', 'done', 'tide', 'mab', 'nib'
          ],
          knownMedicines: [
            'aspirin', 'tylenol', 'advil', 'panadol', 'amoxicillin', 'ibuprofen',
            'acetaminophen', 'paracetamol', 'codeine', 'morphine', 'tramadol',
            'omeprazole', 'lansoprazole', 'pantoprazole', 'esomeprazole',
            'atorvastatin', 'simvastatin', 'rosuvastatin', 'pravastatin',
            'metformin', 'insulin', 'glipizide', 'glyburide', 'pioglitazone'
          ],
          modifiers: [
            'extra', 'strength', 'forte', 'plus', 'max', 'super', 'ultra', 'double',
            'extended', 'release', 'slow', 'controlled', 'sustained', 'delayed',
            'liquid', 'gel', 'cream', 'ointment', 'spray', 'drops', 'syrup',
            'cold', 'flu', 'sinus', 'allergy', 'cough', 'fever', 'pain', 'headache'
          ]
        };
    }
  }

  /**
   * Cleanup resources
   */
  async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      console.log('🔄 Tesseract worker terminated');
    }

    // Cleanup preprocessor
    if (this.preprocessor) {
      this.preprocessor.dispose();
    }
  }
}
